import type { auth } from "@/lib/auth";
import { betterFetch } from "@better-fetch/fetch";
import { type NextRequest, NextResponse } from "next/server";

export const middleware = async (request: NextRequest) => {
  const { pathname } = request.nextUrl;

  if (pathname.startsWith("/api/auth"))
    return NextResponse.next();

  const protectedRoutes = ["/dashboard"];
  const protectedApiRoutes = ["/api/user"];

  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  );

  const isProtectedApiRoute = protectedApiRoutes.some(route =>
    pathname.startsWith(route)
  );

  try {
    const { data: session } = await betterFetch<typeof auth.$Infer.Session>(
      "/api/auth/get-session",
      {
        baseURL: request.nextUrl.origin,
        headers: {
          cookie: request.headers.get("cookie") || "",
        },
      }
    );

    if (isProtectedApiRoute && !session) 
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );

    if (pathname.startsWith("/auth")) {
      if (session) 
        return NextResponse.redirect(new URL("/dashboard", request.url));
      return NextResponse.next();
    }

    if (isProtectedRoute && !session) {
      const authUrl = new URL("/auth", request.url);
      authUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(authUrl);
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    if (isProtectedApiRoute) 
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    if (isProtectedRoute) 
      return NextResponse.redirect(new URL("/auth", request.url));
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (auth API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};