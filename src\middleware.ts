import type { auth } from "@/lib/auth";
import { betterFetch } from "@better-fetch/fetch";
import { type NextRequest, NextResponse } from "next/server";

export const middleware = async (request: NextRequest) => {
  const { pathname } = request.nextUrl;

  // Helper function to check if path matches protected routes
  const isProtectedRoute = (path: string): boolean => {
    return path.startsWith("/dashboard");
  };

  // Helper function to check if path matches protected API routes
  const isProtectedApiRoute = (path: string): boolean => {
    return path.startsWith("/api/user");
  };

  try {
    const { data: session } = await betterFetch<typeof auth.$Infer.Session>(
      "/api/auth/get-session",
      {
        baseURL: request.nextUrl.origin,
        headers: {
          cookie: request.headers.get("cookie") || "",
        },
      }
    );

    // Handle protected API routes
    if (isProtectedApiRoute(pathname) && !session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Handle auth routes - redirect to dashboard if already authenticated
    if (pathname.startsWith("/auth")) {
      if (session)
        return NextResponse.redirect(new URL("/dashboard", request.url));
      return NextResponse.next();
    }
    if (isProtectedRoute(pathname) && !session) {
      const authUrl = new URL("/auth", request.url);
      authUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(authUrl);
    }
    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    if (isProtectedApiRoute(pathname))
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    if (isProtectedRoute(pathname)) 
      return NextResponse.redirect(new URL("/auth", request.url));
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match specific routes that need authentication checking:
     * - /dashboard and its subroutes (protected pages)
     * - /api/user and its subroutes (protected API routes)
     * - /auth and its subroutes (auth pages for redirect logic)
     */
    "/dashboard/:path*",
    "/auth/:path*",
  ],
};