import { Progress } from "@/components/ui/progress";

export default ({
  className,
  variant = "default",
  value = 100,
  ...props
}: React.ComponentProps<"div"> & {
  className?: string;
  value?: number;
  variant?: "retro" | "default";
}) => {
  return (
    <div className={className} {...props}>
      <Progress
        value={value}
        className="h-4 bg-blue-100 dark:bg-blue-900/20"
      />
      <style jsx>{`
        .progress-indicator {
          background-color: #3b82f6 !important; /* blue-500 */
        }
      `}</style>
    </div>
  );
}