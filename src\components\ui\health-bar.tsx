import { Progress } from "@/components/ui/progress";

interface HealthBarProps extends React.ComponentProps<"div"> {
  className?: string;
  value?: number;
  variant?: "retro" | "default";
}

const HealthBar = ({
  className,
  variant = "default",
  value = 100,
  ...props
}: HealthBarProps) => {
  return (
    <div className={className} {...props}>
      <Progress
        value={value}
        className="h-4 bg-red-100 dark:bg-red-900/20"
      />
      <style jsx>{`
        .progress-indicator {
          background-color: #ef4444 !important; /* red-500 */
        }
      `}</style>
    </div>
  );
};

export { HealthBar };
export default HealthBar;

